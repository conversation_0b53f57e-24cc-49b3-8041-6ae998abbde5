package connectrpc

import (
	"context"
	"file_service/internal/infra"
	"fmt"
	"net/http"
	"os"
	"path/filepath"

	"connectrpc.com/connect"
	filev1 "gitlab.com/bds4430521/proto-bds/gen/file/v1"
	"gitlab.com/bds4430521/proto-bds/gen/file/v1/filev1connect"
)

var _ filev1connect.FileServiceHandler = &fileServerHandler{}

type fileServerHandler struct {
}

func NewFileServer(cabin infra.Cabin) (pattern string, handler http.Handler) {
	impl := &fileServerHandler{}
	path, handler := filev1connect.NewFileServiceHandler(impl)
	return path, handler
}

func (f *fileServerHandler) UploadFile(ctx context.Context, stream *connect.ClientStream[filev1.UploadFileChunk]) (*connect.Response[filev1.UploadFileResponse], error) {
	saveDir := "./uploads"
	if err := os.MkdirAll(saveDir, 0o755); err != nil {
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("cannot create upload dir: %w", err))
	}

	var fileName string
	var filePath string
	var file *os.File
	var totalSize int64

	for stream.Receive() { // Receive() trả về bool
		chunk := stream.Msg()

		if file == nil {
			fileName = chunk.GetFileName()
			if fileName == "" {
				return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("file name is required"))
			}
			totalSize = int64(chunk.GetTotalSize())

			filePath = filepath.Join(saveDir, filepath.Base(fileName))

			var err error
			file, err = os.Create(filePath)
			if err != nil {
				return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("cannot create file: %w", err))
			}
			defer file.Close()
		}

		content := chunk.GetContent()
		if len(content) > 0 {
			if _, err := file.Write(content); err != nil {
				return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to write chunk: %w", err))
			}
		}
	}

	if err := stream.Err(); err != nil {
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("error receiving stream: %w", err))
	}

	fileURL := fmt.Sprintf("https://cdn.example.com/uploads/%s", filepath.Base(fileName))
	fmt.Println("File URL:", fileURL)
	fmt.Println("Total size:", totalSize)
	fmt.Println("Type of file:", stream.Msg().MimeType)

	resp := &filev1.UploadFileResponse{
		FileUrl: fileURL,
	}

	return connect.NewResponse(resp), nil
}

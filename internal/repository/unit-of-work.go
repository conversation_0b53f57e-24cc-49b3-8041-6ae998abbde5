package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"
)

type unitOfWork struct {
	db    *gorm.DB
	repos map[string]interface{}
}

func NewUnitOfWork(db *gorm.DB) UnitOfWork {
	return &unitOfWork{
		db:    db,
		repos: make(map[string]interface{}),
	}
}

type UnitOfWork interface {
	ExecTx(ctx context.Context, fn func(uow UnitOfWork) error) error
}

func (u *unitOfWork) ExecTx(ctx context.Context, fn func(uow UnitOfWork) error) error {
	tx := u.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	txUow := &unitOfWork{
		db:    tx,
		repos: make(map[string]interface{}),
	}

	err := fn(txUow)
	if err != nil {
		if rbErr := tx.Rollback().Error; rbErr != nil {
			return fmt.Errorf("tx error: %v, rollback error: %v", err, rbErr)
		}
		return err
	}
	return tx.Commit().Error
}

package main

import (
	"context"
	"file_service/interfaces/v1/connectrpc"
	"file_service/internal/infra"
	"file_service/internal/usecase"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/rs/cors"

	infrastructurecore "github.com/ngochuyk812/building_block/infrastructure/core"
	"github.com/ngochuyk812/building_block/pkg/config"
)

func main() {

	policiesPath := &map[string][]string{}
	config := config.NewConfigEnv()
	config.PoliciesPath = policiesPath
	infraInstance := infrastructurecore.NewInfra(config)
	// db := database.NewSQLDB(config.DbConnect, config.DbName)

	// uof := repository.NewUnitOfWork(db)
	cabin := infra.NewCabin(infraInstance, nil)
	useCases := usecase.NewUsecaseManager(cabin)
	fmt.Print(useCases)
	mux := http.NewServeMux()

	pattern, handler := connectrpc.NewFileServer(cabin)
	mux.Handle(pattern, handler)

	mux.HandleFunc("/healthz", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})
	corsHandler := cors.New(cors.Options{
		AllowedOrigins:   []string{"http://localhost:3000", "https://fe-dashboard-dev.nnh.io.vn"},
		AllowedMethods:   []string{"GET", "POST", "OPTIONS"},
		AllowedHeaders:   []string{"*"},
		AllowCredentials: true,
	}).Handler(mux)
	server := &http.Server{
		Addr:    fmt.Sprintf(":%s", config.Port),
		Handler: corsHandler,
	}
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		ctx := context.Background()
		ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		fmt.Println("Shutting down server...")
		if err := server.Shutdown(ctx); err != nil {
			fmt.Println("Error shutting down server:", err)
		}
		fmt.Println("Server stopped")
	}()

	fmt.Println("Starting server...")
	err := server.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		fmt.Println("Error starting server:", err)
	}
}

#!/bin/bash

# Đ<PERSON>ờng dẫn đến file cần upload
FILE_PATH="./test-file.txt"
FILE_NAME=$(basename "$FILE_PATH")
MIME_TYPE="text/plain"

# Tạo file test nếu chưa tồn tại
if [ ! -f "$FILE_PATH" ]; then
  echo "Creating test file..."
  echo "This is a test file content" > "$FILE_PATH"
fi

# Lấy kích thước file
FILE_SIZE=$(stat -c%s "$FILE_PATH")

# Đọc nội dung file
FILE_CONTENT=$(cat "$FILE_PATH" | base64)

# Tạo payload JSON
JSON_PAYLOAD=$(cat <<EOF
{
  "fileName": "$FILE_NAME",
  "mimeType": "$MIME_TYPE",
  "totalSize": $FILE_SIZE,
  "content": "$FILE_CONTENT"
}
EOF
)

# <PERSON><PERSON><PERSON> request
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Connect-Protocol-Version: 1" \
  -d "$JSON_PAYLOAD" \
  "http://localhost:8083/file.v1.FileService/UploadFile"

echo -e "\nRequest sent!"